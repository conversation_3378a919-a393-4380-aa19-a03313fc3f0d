# 题库管理系统部署指南

## 部署文件清单

部署时需要以下文件：

```
question-bank-manager.exe    # Windows可执行文件
.env                        # 配置文件
static/                     # 静态文件目录
├── index.html
├── pages/
│   ├── login.html
│   └── dashboard.html
└── assets/
    ├── css/
    └── js/
```

## Windows部署步骤

### 1. 准备部署文件

将以下文件复制到目标Windows服务器：
- `question-bank-manager.exe`
- `.env`
- `static/` 目录及其所有内容

### 2. 配置环境

编辑 `.env` 文件，确保所有配置项正确：

```env
# 应用配置
APP_PORT=8080
JWT_SECRET=QmFuay1NZ3ItU2VjcmV0LUtleS0yMDI0
ADMIN_PHONE=***********

# MySQL配置 (确保网络可达)
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api

# Redis配置 (确保网络可达)
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..

# 阿里云服务配置
ALIYUN_ACCESS_KEY_ID=LTAI5tBdn5WuVw49f6VDUo1T
ALIYUN_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_BUCKET=lnterstellar
ALIYUN_OSS_ENDPOINT=oss-cn-zhangjiakou.aliyuncs.com
ALIYUN_SMS_SIGN_NAME=青岛果沐云计算
ALIYUN_SMS_TEMPLATE_CODE=SMS_465946164
```

### 3. 运行应用

#### 方式一：直接运行
```cmd
question-bank-manager.exe
```

#### 方式二：后台运行
```cmd
start /B question-bank-manager.exe
```

#### 方式三：创建批处理文件
创建 `start.bat` 文件：
```batch
@echo off
echo Starting Question Bank Manager...
question-bank-manager.exe
pause
```

### 4. 验证部署

1. 检查应用启动日志
2. 访问 `http://localhost:8080` 或 `http://服务器IP:8080`
3. 测试登录功能（手机号：***********）

## 防火墙配置

确保以下端口开放：
- **8080** - 应用服务端口
- **3380** - MySQL数据库端口（出站）
- **6379** - Redis端口（出站）
- **443** - HTTPS（阿里云服务，出站）

## 系统要求

- **操作系统**: Windows 7/8/10/11 或 Windows Server 2012+
- **内存**: 最少 512MB，推荐 1GB+
- **磁盘空间**: 最少 100MB
- **网络**: 需要访问外部MySQL、Redis和阿里云服务

## 故障排除

### 1. 应用无法启动

**问题**: 双击exe文件后立即关闭
**解决**: 
- 检查 `.env` 文件是否存在
- 使用命令行运行查看错误信息
- 检查端口8080是否被占用

### 2. 数据库连接失败

**问题**: 日志显示MySQL连接失败
**解决**:
- 检查MySQL服务器是否运行
- 验证网络连接和防火墙设置
- 确认数据库配置信息正确

### 3. Redis连接失败

**问题**: 日志显示Redis连接失败
**解决**:
- 检查Redis服务器是否运行
- 验证网络连接和密码
- 确认Redis配置信息正确

### 4. 短信发送失败

**问题**: 验证码发送失败
**解决**:
- 检查阿里云短信服务配置
- 验证AccessKey权限
- 确认短信模板和签名正确

### 5. 图片上传失败

**问题**: 图片上传到OSS失败
**解决**:
- 检查阿里云OSS配置
- 验证Bucket权限设置
- 确认网络连接正常

## 日志查看

应用运行时会输出详细日志，包括：
- 数据库连接状态
- API请求记录
- 错误信息

建议将日志重定向到文件：
```cmd
question-bank-manager.exe > app.log 2>&1
```

## 更新部署

1. 停止当前运行的应用
2. 备份 `local.db` 文件（SQLite数据库）
3. 替换 `question-bank-manager.exe`
4. 重新启动应用

## 安全建议

1. **修改默认JWT密钥**: 更改 `.env` 中的 `JWT_SECRET`
2. **网络安全**: 使用防火墙限制访问
3. **定期备份**: 备份SQLite数据库文件
4. **监控日志**: 定期检查应用日志
5. **更新维护**: 定期更新应用版本

## 技术支持

如遇到部署问题，请提供：
1. 操作系统版本
2. 错误日志信息
3. 网络环境描述
4. 配置文件内容（隐藏敏感信息）
