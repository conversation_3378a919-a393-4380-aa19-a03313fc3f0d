package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	// Redis配置
	RedisHost     string
	RedisPort     string
	RedisPassword string
	RedisDB       int

	// MySQL配置
	MySQLHost     string
	MySQLPort     string
	MySQLUsername string
	MySQLPassword string
	MySQLDatabase string
	MySQLCharset  string

	// 阿里云OSS配置
	AliyunAccessKeyID     string
	AliyunAccessKeySecret string
	AliyunOSSBucket       string
	AliyunOSSEndpoint     string
	AliyunOSSDirectory    string

	// 阿里云短信配置
	AliyunSMSSignName     string
	AliyunSMSTemplateCode string

	// 应用配置
	AppPort    string
	JWTSecret  string
	AdminPhone string

	// SQLite数据库路径
	SQLiteDBPath string
}

var AppConfig *Config

func LoadConfig() {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	redisDB, _ := strconv.Atoi(getEnv("REDIS_DB", "0"))

	AppConfig = &Config{
		// Redis配置
		RedisHost:     getEnv("REDIS_HOST", "localhost"),
		RedisPort:     getEnv("REDIS_PORT", "6379"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       redisDB,

		// MySQL配置
		MySQLHost:     getEnv("MYSQL_HOST", "localhost"),
		MySQLPort:     getEnv("MYSQL_PORT", "3306"),
		MySQLUsername: getEnv("MYSQL_USERNAME", "root"),
		MySQLPassword: getEnv("MYSQL_PASSWORD", ""),
		MySQLDatabase: getEnv("MYSQL_DATABASE", "question_bank"),
		MySQLCharset:  getEnv("MYSQL_CHARSET", "utf8mb4"),

		// 阿里云OSS配置
		AliyunAccessKeyID:     getEnv("ALIYUN_ACCESS_KEY_ID", ""),
		AliyunAccessKeySecret: getEnv("ALIYUN_ACCESS_KEY_SECRET", ""),
		AliyunOSSBucket:       getEnv("ALIYUN_OSS_BUCKET", ""),
		AliyunOSSEndpoint:     getEnv("ALIYUN_OSS_ENDPOINT", ""),
		AliyunOSSDirectory:    getEnv("ALIYUN_OSS_DIRECTORY", ""),

		// 阿里云短信配置
		AliyunSMSSignName:     getEnv("ALIYUN_SMS_SIGN_NAME", ""),
		AliyunSMSTemplateCode: getEnv("ALIYUN_SMS_TEMPLATE_CODE", ""),

		// 应用配置
		AppPort:    getEnv("APP_PORT", "8080"),
		JWTSecret:  getEnv("JWT_SECRET", "default-secret"),
		AdminPhone: getEnv("ADMIN_PHONE", "***********"),

		// SQLite数据库路径
		SQLiteDBPath: getEnv("SQLITE_DB_PATH", "./local.db"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
