<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能管理系统 - 主控台</title>
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 侧边导航栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-brain"></i>
                <span class="logo-text">智能系统</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <ul class="sidebar-menu">
            <li class="menu-item active" data-page="home">
                <a href="#" class="menu-link">
                    <i class="fas fa-home"></i>
                    <span class="menu-text">数据大屏</span>
                </a>
            </li>
            <li class="menu-item" data-page="questions">
                <a href="#" class="menu-link">
                    <i class="fas fa-question-circle"></i>
                    <span class="menu-text">题库管理</span>
                </a>
            </li>
            <li class="menu-item" data-page="users">
                <a href="#" class="menu-link">
                    <i class="fas fa-users"></i>
                    <span class="menu-text">用户管理</span>
                </a>
            </li>
            <li class="menu-item" data-page="analytics">
                <a href="#" class="menu-link">
                    <i class="fas fa-chart-bar"></i>
                    <span class="menu-text">数据分析</span>
                </a>
            </li>
            <li class="menu-item" data-page="settings">
                <a href="#" class="menu-link">
                    <i class="fas fa-cog"></i>
                    <span class="menu-text">系统设置</span>
                </a>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">管理员</span>
                    <span class="user-role">系统管理员</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </button>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">数据大屏</h1>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索...">
                </div>
                <div class="header-actions">
                    <button class="action-btn" title="通知">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </button>
                    <button class="action-btn" title="消息">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">5</span>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 页面内容容器 -->
        <div class="page-container" id="pageContainer">
            <!-- 数据大屏页面 -->
            <div class="page-content active" id="homePage">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>总用户数</h3>
                            <p class="stat-number">12,345</p>
                            <span class="stat-change positive">+12%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>题库总数</h3>
                            <p class="stat-number">8,976</p>
                            <span class="stat-change positive">+8%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>今日访问</h3>
                            <p class="stat-number">2,456</p>
                            <span class="stat-change negative">-3%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="stat-info">
                            <h3>完成率</h3>
                            <p class="stat-number">89.5%</p>
                            <span class="stat-change positive">+5%</span>
                        </div>
                    </div>
                </div>
                
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>用户增长趋势</h3>
                            <div class="chart-actions">
                                <button class="chart-btn">日</button>
                                <button class="chart-btn active">周</button>
                                <button class="chart-btn">月</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="userGrowthChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>题目分类分布</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="activity-section">
                    <div class="activity-card">
                        <h3>最近活动</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <p>新增了 15 道数学题目</p>
                                    <span class="activity-time">2 分钟前</span>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div class="activity-content">
                                    <p>修改了英语题库分类</p>
                                    <span class="activity-time">15 分钟前</span>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <p>新用户注册：张三</p>
                                    <span class="activity-time">1 小时前</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 题库管理页面 -->
            <div class="page-content" id="questionsPage">
                <div class="page-header">
                    <h2>题库管理</h2>
                    <button class="primary-btn" onclick="addQuestion()">
                        <i class="fas fa-plus"></i>
                        添加题目
                    </button>
                </div>
                
                <div class="filters-section">
                    <div class="filter-group">
                        <label>题目类型：</label>
                        <select class="filter-select">
                            <option value="">全部</option>
                            <option value="single">单选题</option>
                            <option value="multiple">多选题</option>
                            <option value="judge">判断题</option>
                            <option value="fill">填空题</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>验证状态：</label>
                        <select class="filter-select">
                            <option value="">全部</option>
                            <option value="verified">已验证</option>
                            <option value="pending">待验证</option>
                            <option value="rejected">已拒绝</option>
                        </select>
                    </div>

                    <div class="search-group">
                        <input type="text" placeholder="搜索题目内容..." class="search-input">
                        <button class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="questions-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>题目内容</th>
                                <th>用户图片</th>
                                <th>题干图片</th>
                                <th>选项</th>
                                <th>正确答案</th>
                                <th>是否验证</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="questionsTableBody">
                            <!-- 题目数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <button class="page-btn" disabled>上一页</button>
                    <span class="page-info">第 1 页，共 10 页</span>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
            
            <!-- 其他页面内容将在这里添加 -->
            <div class="page-content" id="usersPage">
                <h2>用户管理页面</h2>
                <p>用户管理功能正在开发中...</p>
            </div>
            
            <div class="page-content" id="analyticsPage">
                <h2>数据分析页面</h2>
                <p>数据分析功能正在开发中...</p>
            </div>
            
            <div class="page-content" id="settingsPage">
                <h2>系统设置页面</h2>
                <p>系统设置功能正在开发中...</p>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
