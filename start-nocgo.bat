@echo off
title 题库管理系统 - 纯Go版本
color 0A

echo ========================================
echo      题库管理系统 - 纯Go版本
echo      (推荐用于Windows系统)
echo ========================================
echo.

REM 检查纯Go版本可执行文件
if not exist "question-bank-manager-nocgo.exe" (
    echo [错误] 找不到 question-bank-manager-nocgo.exe 文件
    echo.
    echo 请确保以下文件在当前目录下:
    echo - question-bank-manager-nocgo.exe
    echo - .env
    echo - static/ 目录
    echo.
    pause
    exit /b 1
)

REM 检查配置文件
if not exist ".env" (
    echo [错误] 找不到 .env 配置文件
    echo 请确保配置文件在当前目录下
    pause
    exit /b 1
)

REM 检查静态文件目录
if not exist "static" (
    echo [错误] 找不到 static 静态文件目录
    echo 请确保静态文件目录在当前目录下
    pause
    exit /b 1
)

echo [信息] 文件检查完成
echo [信息] 正在启动题库管理系统...
echo [信息] 纯Go版本，无需额外运行时库
echo.
echo 启动后请访问: http://localhost:8080
echo 管理员手机号: ***********
echo.
echo 按 Ctrl+C 可停止程序
echo ========================================

REM 启动纯Go版本
question-bank-manager-nocgo.exe

REM 程序退出后的处理
echo.
echo ========================================
echo 程序已停止运行
echo 退出代码: %ERRORLEVEL%

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [错误] 程序异常退出
    echo 请检查:
    echo 1. 网络连接是否正常
    echo 2. 配置文件是否正确
    echo 3. 端口8080是否被占用
    echo 4. 防火墙是否允许程序运行
    echo.
    echo 如需详细调试信息，请运行: debug.bat
)

echo.
pause
