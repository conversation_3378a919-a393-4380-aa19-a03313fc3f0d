# Windows部署包说明

## 文件清单

Windows部署包包含以下文件：

### 可执行文件
- `question-bank-manager.exe` - 标准版本
- `question-bank-manager-nocgo.exe` - 纯Go版本（推荐）

### 启动脚本
- `start-nocgo.bat` - 启动纯Go版本（推荐）
- `start.bat` - 启动标准版本
- `debug.bat` - 调试模式启动

### 配置文件
- `.env` - 环境配置文件

### 静态文件
- `static/` - Web界面文件目录
  - `index.html` - 主页
  - `pages/` - 页面文件
  - `assets/` - 资源文件

### 文档
- `README.md` - 使用说明
- `DEPLOYMENT.md` - 部署指南
- `TROUBLESHOOTING.md` - 故障排除指南

## 推荐使用方式

### 方式1：双击启动脚本（推荐）
1. 双击 `start-nocgo.bat`
2. 等待程序启动
3. 浏览器访问 `http://localhost:8080`

### 方式2：命令行启动
1. 打开命令提示符
2. 切换到程序目录
3. 运行：`question-bank-manager-nocgo.exe`

### 方式3：调试模式
如果遇到问题，使用调试模式：
1. 双击 `debug.bat`
2. 查看详细的错误信息

## 版本说明

### 标准版本 (question-bank-manager.exe)
- 使用CGO编译
- 可能需要Visual C++运行时库
- 性能稍好，但兼容性较差

### 纯Go版本 (question-bank-manager-nocgo.exe) - 推荐
- 不依赖CGO
- 无需额外运行时库
- 兼容性更好，推荐Windows用户使用

## 系统要求

- **操作系统**: Windows 7/8/10/11 或 Windows Server 2012+
- **架构**: 64位系统
- **内存**: 最少512MB，推荐1GB+
- **磁盘**: 最少100MB可用空间
- **网络**: 需要访问外部MySQL、Redis和阿里云服务

## 安装步骤

1. **解压文件**
   - 将所有文件解压到同一目录
   - 确保目录路径不包含中文字符

2. **检查配置**
   - 打开 `.env` 文件
   - 确认所有配置项正确

3. **启动程序**
   - 双击 `start-nocgo.bat`
   - 或使用命令行启动

4. **验证安装**
   - 浏览器访问 `http://localhost:8080`
   - 使用手机号 *********** 登录

## 常见问题

### Q: 双击exe文件后程序闪退
A: 使用 `start-nocgo.bat` 启动，或运行 `debug.bat` 查看错误信息

### Q: 提示端口被占用
A: 修改 `.env` 文件中的 `APP_PORT=8080` 为其他端口

### Q: 无法连接数据库
A: 检查网络连接和防火墙设置

### Q: 短信发送失败
A: 检查阿里云短信服务配置

## 卸载说明

程序为绿色软件，卸载时：
1. 停止程序运行
2. 删除程序目录
3. 删除生成的 `local.db` 文件（如需要）

## 更新说明

更新程序时：
1. 停止当前运行的程序
2. 备份 `local.db` 文件
3. 替换可执行文件
4. 重新启动程序

## 技术支持

如遇问题，请：
1. 查看 `TROUBLESHOOTING.md` 文档
2. 运行 `debug.bat` 收集错误信息
3. 提供系统信息和错误日志
