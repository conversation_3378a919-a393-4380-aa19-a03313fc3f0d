package services

import (
	"fmt"
	"question-bank-manager/config"
	"question-bank-manager/database"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// AuthService 认证服务
type AuthService struct {
	smsService *SMSService
}

// NewAuthService 创建认证服务实例
func NewAuthService() *AuthService {
	return &AuthService{
		smsService: NewSMSService(),
	}
}

// SendSMS 发送短信验证码
func (a *AuthService) SendSMS(phone string) error {
	cfg := config.AppConfig

	// 验证是否为管理员手机号
	if phone != cfg.AdminPhone {
		return fmt.Errorf("无效的管理员手机号")
	}

	// 检查管理员账号是否存在且激活
	var admin database.Admin
	err := database.SQLiteDB.Where("phone = ? AND is_active = ?", phone, true).First(&admin).Error
	if err != nil {
		return fmt.Errorf("管理员账号不存在或已禁用")
	}

	// 发送验证码
	return a.smsService.SendVerificationCode(phone)
}

// Login 登录验证
func (a *AuthService) Login(phone, code string) (string, int64, error) {
	cfg := config.AppConfig

	// 验证是否为管理员手机号
	if phone != cfg.AdminPhone {
		return "", 0, fmt.Errorf("无效的管理员手机号")
	}

	// 验证验证码
	if err := a.smsService.VerifyCode(phone, code); err != nil {
		return "", 0, err
	}

	// 生成JWT Token
	token, expiresAt, err := a.generateJWTToken(phone)
	if err != nil {
		return "", 0, fmt.Errorf("生成Token失败: %v", err)
	}

	return token, expiresAt, nil
}

// generateJWTToken 生成JWT Token
func (a *AuthService) generateJWTToken(phone string) (string, int64, error) {
	cfg := config.AppConfig
	
	expiresAt := time.Now().Add(24 * time.Hour).Unix() // 24小时过期

	claims := jwt.MapClaims{
		"phone": phone,
		"exp":   expiresAt,
		"iat":   time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(cfg.JWTSecret))
	if err != nil {
		return "", 0, err
	}

	return tokenString, expiresAt, nil
}

// ValidateToken 验证JWT Token
func (a *AuthService) ValidateToken(tokenString string) (*jwt.MapClaims, error) {
	cfg := config.AppConfig

	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(cfg.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return &claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
