package services

import (
	"fmt"
	"mime/multipart"
	"path/filepath"
	"question-bank-manager/config"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
)

// OSSService 阿里云OSS服务
type OSSService struct {
	client *oss.Client
	bucket *oss.Bucket
}

// NewOSSService 创建OSS服务实例
func NewOSSService() (*OSSService, error) {
	cfg := config.AppConfig

	client, err := oss.New(cfg.AliyunOSSEndpoint, cfg.AliyunAccessKeyID, cfg.AliyunAccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	bucket, err := client.Bucket(cfg.AliyunOSSBucket)
	if err != nil {
		return nil, fmt.Errorf("获取OSS存储桶失败: %v", err)
	}

	return &OSSService{
		client: client,
		bucket: bucket,
	}, nil
}

// UploadImage 上传图片到OSS
func (o *OSSService) UploadImage(file *multipart.FileHeader) (string, string, error) {
	cfg := config.AppConfig

	// 验证文件类型
	if !isValidImageType(file.Filename) {
		return "", "", fmt.Errorf("不支持的图片格式，仅支持 jpg, jpeg, png, gif")
	}

	// 验证文件大小 (最大5MB)
	if file.Size > 5*1024*1024 {
		return "", "", fmt.Errorf("图片大小不能超过5MB")
	}

	// 生成唯一文件名
	ext := filepath.Ext(file.Filename)
	fileName := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)
	
	// 构建OSS对象键
	objectKey := fmt.Sprintf("%s/%s", cfg.AliyunOSSDirectory, fileName)

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	// 上传到OSS
	err = o.bucket.PutObject(objectKey, src)
	if err != nil {
		return "", "", fmt.Errorf("上传文件失败: %v", err)
	}

	// 构建访问URL
	imageURL := fmt.Sprintf("https://%s.%s/%s", cfg.AliyunOSSBucket, cfg.AliyunOSSEndpoint, objectKey)

	return fileName, imageURL, nil
}

// DeleteImage 删除OSS中的图片
func (o *OSSService) DeleteImage(fileName string) error {
	cfg := config.AppConfig
	
	objectKey := fmt.Sprintf("%s/%s", cfg.AliyunOSSDirectory, fileName)
	
	err := o.bucket.DeleteObject(objectKey)
	if err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	return nil
}

// isValidImageType 验证图片类型
func isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	
	return false
}
