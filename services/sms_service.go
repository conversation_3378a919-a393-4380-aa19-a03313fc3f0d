package services

import (
	"fmt"
	"log"
	"math/rand"
	"question-bank-manager/config"
	"question-bank-manager/database"
	"strconv"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

// SMSService 短信服务
type SMSService struct{}

// NewSMSService 创建短信服务实例
func NewSMSService() *SMSService {
	return &SMSService{}
}

// SendVerificationCode 发送验证码
func (s *SMSService) SendVerificationCode(phone string) error {
	cfg := config.AppConfig

	// 生成6位随机验证码
	code := generateVerificationCode()

	// 保存验证码到SQLite数据库
	smsCode := database.SMSCode{
		Phone:     phone,
		Code:      code,
		ExpiresAt: time.Now().Add(5 * time.Minute).Unix(), // 5分钟过期
		Used:      false,
	}

	if err := database.SQLiteDB.Create(&smsCode).Error; err != nil {
		return fmt.Errorf("保存验证码失败: %v", err)
	}

	// 发送短信
	client, err := dysmsapi.NewClientWithAccessKey("cn-hangzhou", cfg.AliyunAccessKeyID, cfg.AliyunAccessKeySecret)
	if err != nil {
		return fmt.Errorf("创建短信客户端失败: %v", err)
	}

	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"
	request.PhoneNumbers = phone
	request.SignName = cfg.AliyunSMSSignName
	request.TemplateCode = cfg.AliyunSMSTemplateCode
	request.TemplateParam = fmt.Sprintf(`{"code":"%s"}`, code)

	response, err := client.SendSms(request)
	if err != nil {
		return fmt.Errorf("发送短信失败: %v", err)
	}

	if response.Code != "OK" {
		return fmt.Errorf("短信发送失败: %s", response.Message)
	}

	log.Printf("验证码发送成功，手机号: %s, 验证码: %s", phone, code)
	return nil
}

// VerifyCode 验证验证码
func (s *SMSService) VerifyCode(phone, code string) error {
	var smsCode database.SMSCode

	// 查找最新的未使用的验证码
	err := database.SQLiteDB.Where("phone = ? AND code = ? AND used = ? AND expires_at > ?",
		phone, code, false, time.Now().Unix()).
		Order("created_at DESC").
		First(&smsCode).Error

	if err != nil {
		return fmt.Errorf("验证码无效或已过期")
	}

	// 标记验证码为已使用
	smsCode.Used = true
	if err := database.SQLiteDB.Save(&smsCode).Error; err != nil {
		return fmt.Errorf("更新验证码状态失败: %v", err)
	}

	return nil
}

// generateVerificationCode 生成6位数字验证码
func generateVerificationCode() string {
	rand.Seed(time.Now().UnixNano())
	code := rand.Intn(900000) + 100000 // 生成100000-999999之间的数字
	return strconv.Itoa(code)
}
