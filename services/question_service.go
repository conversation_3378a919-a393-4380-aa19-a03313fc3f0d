package services

import (
	"encoding/json"
	"fmt"
	"question-bank-manager/database"
	"question-bank-manager/models"
	"time"

	"gorm.io/gorm"
)

// QuestionService 题库服务
type QuestionService struct{}

// NewQuestionService 创建题库服务实例
func NewQuestionService() *QuestionService {
	return &QuestionService{}
}

// GetQuestionList 获取题目列表
func (q *QuestionService) GetQuestionList(req *models.QuestionListRequest) (*models.QuestionListResponse, error) {
	var questions []models.Question
	var total int64

	// 构建查询条件
	query := database.MySQLDB.Model(&models.Question{})

	// 搜索条件
	if req.Search != "" {
		query = query.Where("question_text LIKE ?", "%"+req.Search+"%")
	}

	// 题目类型筛选
	if req.QuestionType != "" {
		query = query.Where("question_type = ?", req.QuestionType)
	}

	// 验证状态筛选
	if req.IsVerified != nil {
		query = query.Where("is_verified = ?", *req.IsVerified)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&questions).Error; err != nil {
		return nil, fmt.Errorf("获取题目列表失败: %v", err)
	}

	// 计算总页数
	totalPage := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPage++
	}

	return &models.QuestionListResponse{
		Questions: questions,
		Total:     total,
		Page:      req.Page,
		PageSize:  req.PageSize,
		TotalPage: totalPage,
	}, nil
}

// GetQuestionByID 根据ID获取题目详情
func (q *QuestionService) GetQuestionByID(id uint64) (*models.Question, error) {
	var question models.Question
	
	if err := database.MySQLDB.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("题目不存在")
		}
		return nil, fmt.Errorf("获取题目详情失败: %v", err)
	}

	return &question, nil
}

// CreateQuestion 创建题目
func (q *QuestionService) CreateQuestion(req *models.QuestionRequest) (*models.Question, error) {
	// 验证cache_key_hash是否存在
	var count int64
	if err := database.MySQLDB.Model(&models.Question{}).
		Where("cache_key_hash = ?", req.CacheKeyHash).
		Count(&count).Error; err != nil {
		return nil, fmt.Errorf("验证缓存键失败: %v", err)
	}

	if count == 0 {
		return nil, fmt.Errorf("键值对错误，并未查询到关联问题")
	}

	// 转换答案为JSON
	answerJSON, err := json.Marshal(req.Answer)
	if err != nil {
		return nil, fmt.Errorf("答案格式错误: %v", err)
	}

	now := time.Now()
	question := models.Question{
		CacheKeyHash: req.CacheKeyHash,
		QuestionType: req.QuestionType,
		QuestionText: req.QuestionText,
		OptionA:      req.OptionA,
		OptionB:      req.OptionB,
		OptionC:      req.OptionC,
		OptionD:      req.OptionD,
		OptionY:      req.OptionY,
		OptionN:      req.OptionN,
		Answer:       answerJSON,
		Analysis:     req.Analysis,
		UserImage:    req.UserImage,
		ImageURL:     "", // 新增时为空
		IsVerified:   req.IsVerified,
		CreatedAt:    &now,
		UpdatedAt:    &now,
	}

	if err := database.MySQLDB.Create(&question).Error; err != nil {
		return nil, fmt.Errorf("创建题目失败: %v", err)
	}

	// 清除Redis缓存
	if err := database.ClearCacheByKey(req.CacheKeyHash); err != nil {
		// 记录日志但不影响主流程
		fmt.Printf("清除缓存失败: %v\n", err)
	}

	return &question, nil
}

// UpdateQuestion 更新题目
func (q *QuestionService) UpdateQuestion(id uint64, req *models.QuestionRequest) (*models.Question, error) {
	// 获取原题目
	var question models.Question
	if err := database.MySQLDB.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("题目不存在")
		}
		return nil, fmt.Errorf("获取题目失败: %v", err)
	}

	// 转换答案为JSON
	answerJSON, err := json.Marshal(req.Answer)
	if err != nil {
		return nil, fmt.Errorf("答案格式错误: %v", err)
	}

	// 更新字段
	now := time.Now()
	question.QuestionType = req.QuestionType
	question.QuestionText = req.QuestionText
	question.OptionA = req.OptionA
	question.OptionB = req.OptionB
	question.OptionC = req.OptionC
	question.OptionD = req.OptionD
	question.OptionY = req.OptionY
	question.OptionN = req.OptionN
	question.Answer = answerJSON
	question.Analysis = req.Analysis
	question.UserImage = req.UserImage
	question.IsVerified = req.IsVerified
	question.UpdatedAt = &now

	if err := database.MySQLDB.Save(&question).Error; err != nil {
		return nil, fmt.Errorf("更新题目失败: %v", err)
	}

	// 清除Redis缓存
	if err := database.ClearCacheByKey(question.CacheKeyHash); err != nil {
		// 记录日志但不影响主流程
		fmt.Printf("清除缓存失败: %v\n", err)
	}

	return &question, nil
}

// DeleteQuestion 删除题目
func (q *QuestionService) DeleteQuestion(id uint64) error {
	var question models.Question
	if err := database.MySQLDB.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("题目不存在")
		}
		return fmt.Errorf("获取题目失败: %v", err)
	}

	if err := database.MySQLDB.Delete(&question).Error; err != nil {
		return fmt.Errorf("删除题目失败: %v", err)
	}

	// 清除Redis缓存
	if err := database.ClearCacheByKey(question.CacheKeyHash); err != nil {
		// 记录日志但不影响主流程
		fmt.Printf("清除缓存失败: %v\n", err)
	}

	return nil
}

// UpdateVerifyStatus 更新验证状态
func (q *QuestionService) UpdateVerifyStatus(id uint64, isVerified int8) error {
	var question models.Question
	if err := database.MySQLDB.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("题目不存在")
		}
		return fmt.Errorf("获取题目失败: %v", err)
	}

	now := time.Now()
	question.IsVerified = isVerified
	question.UpdatedAt = &now

	if err := database.MySQLDB.Save(&question).Error; err != nil {
		return fmt.Errorf("更新验证状态失败: %v", err)
	}

	// 清除Redis缓存
	if err := database.ClearCacheByKey(question.CacheKeyHash); err != nil {
		// 记录日志但不影响主流程
		fmt.Printf("清除缓存失败: %v\n", err)
	}

	return nil
}
