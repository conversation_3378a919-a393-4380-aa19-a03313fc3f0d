package handlers

import (
	"question-bank-manager/models"
	"question-bank-manager/services"
	"question-bank-manager/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QuestionHandler 题库处理器
type QuestionHandler struct {
	questionService *services.QuestionService
}

// NewQuestionHandler 创建题库处理器实例
func NewQuestionHandler() *QuestionHandler {
	return &QuestionHandler{
		questionService: services.NewQuestionService(),
	}
}

// GetQuestionList 获取题目列表
func (h *QuestionHandler) GetQuestionList(c *gin.Context) {
	var req models.QuestionListRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 获取题目列表
	response, err := h.questionService.GetQuestionList(&req)
	if err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.Success(c, response)
}

// GetQuestionByID 获取题目详情
func (h *QuestionHandler) GetQuestionByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	question, err := h.questionService.GetQuestionByID(id)
	if err != nil {
		utils.NotFound(c, err.Error())
		return
	}

	utils.Success(c, question)
}

// CreateQuestion 创建题目
func (h *QuestionHandler) CreateQuestion(c *gin.Context) {
	var req models.QuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证请求数据
	if err := utils.ValidateQuestionRequest(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 创建题目
	question, err := h.questionService.CreateQuestion(&req)
	if err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目创建成功", question)
}

// UpdateQuestion 更新题目
func (h *QuestionHandler) UpdateQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	var req models.QuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证请求数据
	if err := utils.ValidateQuestionRequest(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 更新题目
	question, err := h.questionService.UpdateQuestion(id, &req)
	if err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目更新成功", question)
}

// DeleteQuestion 删除题目
func (h *QuestionHandler) DeleteQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	// 删除题目
	if err := h.questionService.DeleteQuestion(id); err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目删除成功", nil)
}

// UpdateVerifyStatus 更新验证状态
func (h *QuestionHandler) UpdateVerifyStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	var req models.VerifyStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 更新验证状态
	if err := h.questionService.UpdateVerifyStatus(id, req.IsVerified); err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "验证状态更新成功", nil)
}
