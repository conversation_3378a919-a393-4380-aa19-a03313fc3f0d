package handlers

import (
	"question-bank-manager/models"
	"question-bank-manager/services"
	"question-bank-manager/utils"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
}

// NewAuthHandler 创建认证处理器实例
func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		authService: services.NewAuthService(),
	}
}

// SendSMS 发送短信验证码
func (h *AuthHandler) SendSMS(c *gin.Context) {
	var req models.SendSMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		utils.BadRequest(c, "手机号格式错误")
		return
	}

	// 发送短信
	if err := h.authService.SendSMS(req.Phone); err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "验证码发送成功", models.SMSResponse{
		Message: "验证码已发送，请注意查收",
		Success: true,
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		utils.BadRequest(c, "手机号格式错误")
		return
	}

	// 登录验证
	token, expiresAt, err := h.authService.Login(req.Phone, req.Code)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "登录成功", models.LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		Phone:     req.Phone,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 由于使用JWT，登出主要在前端处理（删除token）
	// 这里只是返回成功响应
	utils.SuccessWithMessage(c, "登出成功", nil)
}
