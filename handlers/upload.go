package handlers

import (
	"question-bank-manager/services"
	"question-bank-manager/utils"

	"github.com/gin-gonic/gin"
)

// UploadHandler 文件上传处理器
type UploadHandler struct {
	ossService *services.OSSService
}

// NewUploadHandler 创建文件上传处理器实例
func NewUploadHandler() (*UploadHandler, error) {
	ossService, err := services.NewOSSService()
	if err != nil {
		return nil, err
	}

	return &UploadHandler{
		ossService: ossService,
	}, nil
}

// UploadImage 上传图片
func (h *UploadHandler) UploadImage(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		utils.BadRequest(c, "请选择要上传的图片")
		return
	}

	// 上传到OSS
	fileName, imageURL, err := h.ossService.UploadImage(file)
	if err != nil {
		utils.InternalServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "图片上传成功", gin.H{
		"file_name": fileName,
		"image_url": imageURL,
	})
}
