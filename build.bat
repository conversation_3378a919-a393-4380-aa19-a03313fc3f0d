@echo off
echo Building Question Bank Manager for Windows...

REM 设置环境变量
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=1

REM 清理之前的构建
if exist "question-bank-manager.exe" del "question-bank-manager.exe"

REM 构建应用程序
go build -ldflags "-s -w" -o question-bank-manager.exe .

if %ERRORLEVEL% EQU 0 (
    echo Build successful! 
    echo Executable: question-bank-manager.exe
    echo.
    echo To run the application:
    echo 1. Make sure .env file is in the same directory
    echo 2. Run: question-bank-manager.exe
    echo 3. Open browser and go to: http://localhost:8080
) else (
    echo Build failed!
    exit /b 1
)

pause
