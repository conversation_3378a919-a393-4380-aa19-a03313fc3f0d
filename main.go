package main

import (
	"log"
	"question-bank-manager/config"
	"question-bank-manager/database"
	"question-bank-manager/handlers"
	"question-bank-manager/middleware"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	config.LoadConfig()

	// 初始化数据库连接
	database.InitMySQL()
	database.InitSQLite()
	database.InitRedis()

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	r := gin.Default()

	// 添加中间件
	r.Use(middleware.CORSMiddleware())

	// 静态文件服务
	r.Static("/static", "./static")
	r.StaticFile("/", "./static/index.html")

	// 创建处理器实例
	authHandler := handlers.NewAuthHandler()
	questionHandler := handlers.NewQuestionHandler()
	
	uploadHandler, err := handlers.NewUploadHandler()
	if err != nil {
		log.Fatalf("Failed to create upload handler: %v", err)
	}

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/send-sms", authHandler.SendSMS)
			auth.POST("/login", authHandler.Login)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// 认证相关
			protected.POST("/auth/logout", authHandler.Logout)

			// 题库管理
			questions := protected.Group("/questions")
			{
				questions.GET("", questionHandler.GetQuestionList)
				questions.GET("/:id", questionHandler.GetQuestionByID)
				questions.POST("", questionHandler.CreateQuestion)
				questions.PUT("/:id", questionHandler.UpdateQuestion)
				questions.DELETE("/:id", questionHandler.DeleteQuestion)
				questions.PUT("/:id/verify", questionHandler.UpdateVerifyStatus)
			}

			// 文件上传
			protected.POST("/upload/image", uploadHandler.UploadImage)
		}
	}

	// 启动服务器
	port := ":" + config.AppConfig.AppPort
	log.Printf("Server starting on port %s", port)
	if err := r.Run(port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
