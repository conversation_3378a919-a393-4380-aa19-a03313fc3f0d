// 登录页面JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phoneNumber');
    const codeInput = document.getElementById('verificationCode');
    const getCodeBtn = document.getElementById('getCodeBtn');
    const loginBtn = document.getElementById('loginBtn');
    const loginForm = document.getElementById('loginForm');
    const phoneError = document.getElementById('phoneError');
    const codeError = document.getElementById('codeError');
    const successMessage = document.getElementById('successMessage');

    let countdown = 0;
    let countdownTimer = null;

    // 手机号验证
    function validatePhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }

    // 验证码验证
    function validateCode(code) {
        return code.length === 6 && /^\d{6}$/.test(code);
    }

    // 显示错误信息
    function showError(element, message) {
        element.textContent = message;
        element.classList.add('show');
    }

    // 隐藏错误信息
    function hideError(element) {
        element.classList.remove('show');
    }

    // 更新按钮状态
    function updateButtonStates() {
        const phoneValid = validatePhone(phoneInput.value);
        const codeValid = validateCode(codeInput.value);
        
        getCodeBtn.disabled = !phoneValid || countdown > 0;
        loginBtn.disabled = !phoneValid || !codeValid;
    }

    // 倒计时功能
    function startCountdown() {
        countdown = 60;
        getCodeBtn.disabled = true;
        
        countdownTimer = setInterval(() => {
            countdown--;
            getCodeBtn.textContent = `${countdown}秒后重试`;
            
            if (countdown <= 0) {
                clearInterval(countdownTimer);
                getCodeBtn.textContent = '获取验证码';
                updateButtonStates();
            }
        }, 1000);
    }

    // 手机号输入事件
    phoneInput.addEventListener('input', function() {
        const phone = this.value.replace(/\D/g, '');
        this.value = phone;
        
        hideError(phoneError);
        
        if (phone.length > 0 && !validatePhone(phone)) {
            if (phone.length < 11) {
                showError(phoneError, '请输入完整的手机号码');
            } else {
                showError(phoneError, '请输入正确的手机号码');
            }
        }
        
        updateButtonStates();
    });

    // 验证码输入事件
    codeInput.addEventListener('input', function() {
        const code = this.value.replace(/\D/g, '');
        this.value = code;
        
        hideError(codeError);
        
        if (code.length > 0 && !validateCode(code)) {
            showError(codeError, '验证码应为6位数字');
        }
        
        updateButtonStates();
    });

    // 获取验证码
    getCodeBtn.addEventListener('click', function() {
        if (!validatePhone(phoneInput.value)) {
            showError(phoneError, '请输入正确的手机号码');
            return;
        }
        
        // 模拟发送验证码
        console.log('发送验证码到:', phoneInput.value);
        startCountdown();
        
        // 这里可以添加实际的API调用
        // sendVerificationCode(phoneInput.value);
    });

    // 表单提交
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const phone = phoneInput.value;
        const code = codeInput.value;
        
        // 验证手机号
        if (!validatePhone(phone)) {
            showError(phoneError, '请输入正确的手机号码');
            return;
        }
        
        // 验证验证码
        if (!validateCode(code)) {
            showError(codeError, '请输入6位验证码');
            return;
        }
        
        // 模拟登录验证
        console.log('登录验证:', { phone, code });
        
        // 显示成功消息
        successMessage.classList.add('show');
        
        // 2秒后跳转到主界面
        setTimeout(() => {
            window.location.href = './dashboard.html';
        }, 2000);
        
        // 这里可以添加实际的API调用
        // loginWithCode(phone, code);
    });

    // 初始化按钮状态
    updateButtonStates();
});
