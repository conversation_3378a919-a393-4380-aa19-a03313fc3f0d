* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 400px;
    position: relative;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.logo i {
    font-size: 32px;
    color: white;
}

.login-header h1 {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 15px;
    color: #999;
    font-size: 16px;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 15px;
    padding-left: 45px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
    outline: none;
}

.input-wrapper input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-wrapper input::placeholder {
    color: #999;
}

.verification-wrapper {
    background: white;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    position: relative;
}

.verification-wrapper input {
    border-radius: 0;
    padding: 16px 15px;
    border-right: none;
    padding-right: 140px;
    padding-left: 45px;
}

.verification-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
}

.get-code-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: 2px solid #667eea;
    border-left: none;
    padding: 16px 20px;
    border-radius: 0 10px 10px 0;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
    flex-shrink: 0;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.get-code-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    border-color: #5a6fd8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.get-code-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    border-color: #ccc;
    color: #666;
    transform: none;
    box-shadow: none;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    margin-left: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-message.show {
    opacity: 1;
}

.login-footer {
    text-align: center;
}

.login-footer p {
    color: #999;
    font-size: 12px;
}

.success-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #27ae60;
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.success-message.show {
    opacity: 1;
    visibility: visible;
}

.success-message i {
    font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .login-card {
        padding: 30px 20px;
        border-radius: 15px;
    }
    
    .verification-wrapper {
        flex-direction: column;
        align-items: stretch;
        padding: 0;
        position: relative;
    }

    .verification-wrapper input {
        margin-bottom: 0;
        border-radius: 12px 12px 0 0;
        border-bottom: none;
        padding: 16px 15px;
        padding-right: 15px;
        padding-left: 45px;
    }

    .verification-wrapper i {
        position: absolute;
        left: 15px;
        top: 16px;
        transform: none;
        z-index: 1;
    }

    .get-code-btn {
        margin-left: 0;
        width: 100%;
        min-width: auto;
        border-radius: 0 0 12px 12px;
        border: 2px solid #667eea;
        border-top: none;
        padding: 16px 20px;
        position: static;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
