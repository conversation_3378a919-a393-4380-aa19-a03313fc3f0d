package models

// LoginRequest 登录请求结构
type LoginRequest struct {
	Phone string `json:"phone" binding:"required,len=11"`
	Code  string `json:"code" binding:"required,len=6"`
}

// SendSMSRequest 发送短信请求结构
type SendSMSRequest struct {
	Phone string `json:"phone" binding:"required,len=11"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
	Phone     string `json:"phone"`
}

// SMSResponse 短信发送响应结构
type SMSResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}
