package models

import (
	"encoding/json"
	"time"
)

// Question 题目模型，对应MySQL中的questions表
type Question struct {
	ID           uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	CacheKeyHash string          `json:"cache_key_hash" gorm:"not null;index"`
	QuestionType string          `json:"question_type" gorm:"not null;index"`
	QuestionText string          `json:"question_text" gorm:"type:text;not null"`
	OptionA      *string         `json:"option_a" gorm:"type:text"`
	OptionB      *string         `json:"option_b" gorm:"type:text"`
	OptionC      *string         `json:"option_c" gorm:"type:text"`
	OptionD      *string         `json:"option_d" gorm:"type:text"`
	OptionY      *string         `json:"option_y" gorm:"type:text"`
	OptionN      *string         `json:"option_n" gorm:"type:text"`
	Answer       json.RawMessage `json:"answer" gorm:"type:json"`
	Analysis     string          `json:"analysis" gorm:"type:text"`
	UserImage    *string         `json:"user_image" gorm:"size:500"`
	ImageURL     string          `json:"image_url" gorm:"size:1000;not null"`
	QwenRaw      json.RawMessage `json:"qwen_raw" gorm:"type:json"`
	DeepseekRaw  json.RawMessage `json:"deepseek_raw" gorm:"type:json"`
	QwenParsed   json.RawMessage `json:"qwen_parsed" gorm:"type:json"`
	IsVerified   int8            `json:"is_verified" gorm:"default:0"`
	CreatedAt    *time.Time      `json:"created_at" gorm:"precision:3"`
	UpdatedAt    *time.Time      `json:"updated_at" gorm:"precision:3"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// QuestionRequest 创建/更新题目的请求结构
type QuestionRequest struct {
	CacheKeyHash string            `json:"cache_key_hash" binding:"required"`
	QuestionType string            `json:"question_type" binding:"required,oneof=单选题 多选题 判断题"`
	QuestionText string            `json:"question_text" binding:"required"`
	OptionA      *string           `json:"option_a"`
	OptionB      *string           `json:"option_b"`
	OptionC      *string           `json:"option_c"`
	OptionD      *string           `json:"option_d"`
	OptionY      *string           `json:"option_y"`
	OptionN      *string           `json:"option_n"`
	Answer       map[string]string `json:"answer" binding:"required"`
	Analysis     string            `json:"analysis" binding:"required"`
	UserImage    *string           `json:"user_image"`
	IsVerified   int8              `json:"is_verified" binding:"oneof=0 1"`
}

// QuestionListRequest 题目列表查询请求
type QuestionListRequest struct {
	Page         int    `form:"page" binding:"min=1"`
	PageSize     int    `form:"page_size" binding:"min=1,max=100"`
	QuestionType string `form:"question_type"`
	IsVerified   *int8  `form:"is_verified"`
	Search       string `form:"search"`
}

// QuestionListResponse 题目列表响应
type QuestionListResponse struct {
	Questions []Question `json:"questions"`
	Total     int64      `json:"total"`
	Page      int        `json:"page"`
	PageSize  int        `json:"page_size"`
	TotalPage int        `json:"total_page"`
}

// VerifyStatusRequest 验证状态更新请求
type VerifyStatusRequest struct {
	IsVerified int8 `json:"is_verified" binding:"oneof=0 1"`
}
