# 题库管理系统

基于Go语言开发的题库管理系统，支持题目的增删改查、图片上传、短信验证登录等功能。

## 功能特性

- 🔐 **安全认证**: 基于短信验证码的登录系统
- 📚 **题库管理**: 支持单选题、多选题、判断题的完整管理
- 🖼️ **图片上传**: 集成阿里云OSS，支持题目配图
- 🔍 **搜索筛选**: 支持题目内容搜索和多维度筛选
- ⚡ **缓存同步**: 自动清理Redis缓存，保证数据一致性
- 💻 **跨平台**: 支持Windows可执行文件部署

## 技术栈

- **后端**: Go + Gin Web框架
- **数据库**: MySQL 8 (远程) + SQLite (本地认证)
- **缓存**: Redis
- **文件存储**: 阿里云OSS
- **短信服务**: 阿里云短信
- **前端**: HTML + CSS + JavaScript

## 快速开始

### 1. 配置环境

确保 `.env` 文件包含正确的配置信息：

```env
# Redis配置
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0

# MySQL配置
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4

# 阿里云OSS配置
ALIYUN_ACCESS_KEY_ID=LTAI5tBdn5WuVw49f6VDUo1T
ALIYUN_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_BUCKET=lnterstellar
ALIYUN_OSS_ENDPOINT=oss-cn-zhangjiakou.aliyuncs.com
ALIYUN_OSS_DIRECTORY=upimggeuc

# 阿里云短信配置
ALIYUN_SMS_SIGN_NAME=青岛果沐云计算
ALIYUN_SMS_TEMPLATE_CODE=SMS_465946164

# 应用配置
APP_PORT=8080
JWT_SECRET=QmFuay1NZ3ItU2VjcmV0LUtleS0yMDI0
ADMIN_PHONE=***********

# SQLite数据库路径
SQLITE_DB_PATH=./local.db
```

### 2. 构建应用

#### Windows构建
```bash
# 运行构建脚本
build.bat
```

#### 手动构建
```bash
# 下载依赖
go mod tidy

# 构建可执行文件
go build -o question-bank-manager .
```

### 3. 运行应用

#### Windows系统（推荐）
```cmd
# 使用纯Go版本（推荐，无需额外依赖）
start-nocgo.bat

# 或直接运行
question-bank-manager-nocgo.exe
```

#### 如果遇到闪退问题
```cmd
# 使用调试脚本查看详细错误
debug.bat

# 或使用标准版本
question-bank-manager.exe
```

#### Linux/macOS系统
```bash
# 直接运行
./question-bank-manager
```

### 4. 访问系统

打开浏览器访问: `http://localhost:8080`

默认管理员手机号: `***********`

## API接口

### 认证接口

- `POST /api/auth/send-sms` - 发送短信验证码
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 题库管理接口

- `GET /api/questions` - 获取题目列表
- `GET /api/questions/:id` - 获取题目详情
- `POST /api/questions` - 创建题目
- `PUT /api/questions/:id` - 更新题目
- `DELETE /api/questions/:id` - 删除题目
- `PUT /api/questions/:id/verify` - 更新验证状态

### 文件上传接口

- `POST /api/upload/image` - 上传图片

## 数据库结构

系统使用两个数据库：

1. **MySQL** - 存储题目数据（questions表）
2. **SQLite** - 存储管理员账号和短信验证码

## 题目类型说明

- **单选题**: 使用ABCD选项，答案为单个选项
- **多选题**: 使用ABCD选项，答案为多个选项
- **判断题**: 使用YN选项，答案为单个选项

## 注意事项

1. 确保MySQL、Redis服务正常运行
2. 阿里云OSS和短信服务配置正确
3. 管理员手机号固定为配置文件中的号码
4. 题目的cache_key_hash必须在数据库中已存在
5. 新增或修改题目后会自动清理相关Redis缓存

## 故障排除

### Windows闪退问题
如果在Windows上双击exe文件后程序立即闪退：

1. **使用纯Go版本（推荐）**:
   ```cmd
   start-nocgo.bat
   ```

2. **使用调试脚本查看错误**:
   ```cmd
   debug.bat
   ```

3. **安装Visual C++运行时库**:
   - 下载: https://aka.ms/vs/17/release/vc_redist.x64.exe
   - 安装后重试

4. **检查端口占用**:
   ```cmd
   netstat -an | find "8080"
   ```

详细解决方案请参考: [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

### 其他常见问题
1. **数据库连接失败**: 检查MySQL和Redis配置是否正确
2. **短信发送失败**: 检查阿里云短信服务配置
3. **图片上传失败**: 检查阿里云OSS配置
4. **端口占用**: 修改.env文件中的APP_PORT配置

## 开发说明

项目采用分层架构：

- `config/` - 配置管理
- `database/` - 数据库连接
- `models/` - 数据模型
- `services/` - 业务逻辑
- `handlers/` - 路由处理
- `middleware/` - 中间件
- `utils/` - 工具函数
- `static/` - 静态文件
