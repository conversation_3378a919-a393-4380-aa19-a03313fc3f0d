@echo off
title 题库管理系统 - 详细调试
color 0C

echo ========================================
echo        题库管理系统 - 详细调试
echo ========================================
echo.

echo [步骤1] 检查系统环境
echo 操作系统: %OS%
echo 处理器架构: %PROCESSOR_ARCHITECTURE%
echo 当前用户: %USERNAME%
echo 当前目录: %CD%
echo.

echo [步骤2] 检查必需文件
if exist "question-bank-manager.exe" (
    echo ✓ question-bank-manager.exe 存在
    for %%A in (question-bank-manager.exe) do echo   文件大小: %%~zA 字节
) else (
    echo ✗ question-bank-manager.exe 不存在
    goto :error
)

if exist ".env" (
    echo ✓ .env 配置文件存在
) else (
    echo ✗ .env 配置文件不存在
    goto :error
)

if exist "static" (
    echo ✓ static 目录存在
) else (
    echo ✗ static 目录不存在
    goto :error
)
echo.

echo [步骤3] 检查端口占用
echo 检查端口8080是否被占用...
netstat -an | find "8080" > nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠ 警告: 端口8080可能被占用
    netstat -an | find "8080"
) else (
    echo ✓ 端口8080可用
)
echo.

echo [步骤4] 尝试启动应用程序
echo 正在启动，请等待...
echo ========================================

REM 设置详细的错误输出
set GODEBUG=netdns=go+2
question-bank-manager.exe

echo ========================================
echo 程序退出代码: %ERRORLEVEL%

if %ERRORLEVEL% EQU 0 (
    echo ✓ 程序正常退出
) else (
    echo ✗ 程序异常退出
    echo.
    echo 常见问题解决方案:
    echo 1. 如果提示缺少DLL文件，请安装 Visual C++ Redistributable
    echo 2. 如果网络连接失败，请检查防火墙设置
    echo 3. 如果端口被占用，请修改.env文件中的APP_PORT
    echo 4. 如果权限不足，请以管理员身份运行
)

goto :end

:error
echo.
echo ✗ 文件检查失败，请确保所有必需文件都在当前目录下
echo.
echo 当前目录内容:
dir /b

:end
echo.
echo 按任意键退出...
pause > nul
