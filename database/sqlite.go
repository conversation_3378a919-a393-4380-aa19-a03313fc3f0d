package database

import (
	"log"
	"question-bank-manager/config"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var SQLiteDB *gorm.DB

func InitSQLite() {
	cfg := config.AppConfig
	
	var err error
	SQLiteDB, err = gorm.Open(sqlite.Open(cfg.SQLiteDBPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		log.Fatalf("Failed to connect to SQLite database: %v", err)
	}

	// 自动迁移表结构
	err = SQLiteDB.AutoMigrate(&Admin{}, &SMSCode{})
	if err != nil {
		log.Fatalf("Failed to migrate SQLite database: %v", err)
	}

	// 初始化管理员账号
	initAdminAccount()

	log.Println("SQLite database connected and initialized successfully")
}

// Admin 管理员表结构
type Admin struct {
	ID          uint   `gorm:"primaryKey"`
	Phone       string `gorm:"unique;not null"`
	IsActive    bool   `gorm:"default:true"`
	CreatedAt   int64  `gorm:"autoCreateTime"`
	UpdatedAt   int64  `gorm:"autoUpdateTime"`
}

// SMSCode 短信验证码表结构
type SMSCode struct {
	ID        uint   `gorm:"primaryKey"`
	Phone     string `gorm:"not null"`
	Code      string `gorm:"not null"`
	ExpiresAt int64  `gorm:"not null"`
	Used      bool   `gorm:"default:false"`
	CreatedAt int64  `gorm:"autoCreateTime"`
}

// 初始化管理员账号
func initAdminAccount() {
	cfg := config.AppConfig
	
	var admin Admin
	result := SQLiteDB.Where("phone = ?", cfg.AdminPhone).First(&admin)
	
	if result.Error != nil {
		// 管理员账号不存在，创建一个
		admin = Admin{
			Phone:    cfg.AdminPhone,
			IsActive: true,
		}
		
		if err := SQLiteDB.Create(&admin).Error; err != nil {
			log.Printf("Failed to create admin account: %v", err)
		} else {
			log.Printf("Admin account created: %s", cfg.AdminPhone)
		}
	} else {
		log.Printf("Admin account already exists: %s", cfg.AdminPhone)
	}
}
