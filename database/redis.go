package database

import (
	"context"
	"fmt"
	"log"
	"question-bank-manager/config"

	"github.com/go-redis/redis/v8"
)

var RedisClient *redis.Client
var ctx = context.Background()

func InitRedis() {
	cfg := config.AppConfig
	
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.RedisHost, cfg.RedisPort),
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,
	})

	// 测试连接
	_, err := RedisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	log.Println("Redis connected successfully")
}

// ClearCacheByKey 根据缓存键清除Redis中的数据
func ClearCacheByKey(cacheKeyHash string) error {
	// 使用模式匹配删除相关的缓存键
	keys, err := RedisClient.Keys(ctx, "*"+cacheKeyHash+"*").Result()
	if err != nil {
		return fmt.Errorf("failed to get keys: %v", err)
	}

	if len(keys) > 0 {
		_, err = RedisClient.Del(ctx, keys...).Result()
		if err != nil {
			return fmt.Errorf("failed to delete keys: %v", err)
		}
		log.Printf("Cleared %d cache keys for hash: %s", len(keys), cacheKeyHash)
	}

	return nil
}
