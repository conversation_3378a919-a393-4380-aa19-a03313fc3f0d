# Windows闪退问题排查指南

## 问题描述
在Windows系统中双击`question-bank-manager.exe`后程序立即闪退，无法正常启动。

## 解决方案

### 方案1：使用调试启动脚本
1. 使用提供的`debug.bat`脚本启动程序
2. 该脚本会显示详细的错误信息，帮助定位问题

```cmd
debug.bat
```

### 方案2：使用无CGO版本
我们提供了两个版本的可执行文件：
- `question-bank-manager.exe` - 标准版本（可能需要C运行时库）
- `question-bank-manager-nocgo.exe` - 纯Go版本（推荐用于Windows）

尝试使用纯Go版本：
```cmd
question-bank-manager-nocgo.exe
```

### 方案3：命令行启动查看错误
1. 按`Win + R`打开运行对话框
2. 输入`cmd`并按回车
3. 使用`cd`命令切换到程序所在目录
4. 直接运行程序查看错误信息：

```cmd
cd /d "C:\path\to\your\program"
question-bank-manager.exe
```

## 常见问题及解决方法

### 1. 缺少Visual C++运行时库
**错误现象**: 程序闪退，没有任何提示

**解决方法**: 
- 下载并安装Microsoft Visual C++ Redistributable
- 链接: https://aka.ms/vs/17/release/vc_redist.x64.exe
- 或者使用`question-bank-manager-nocgo.exe`版本

### 2. 端口被占用
**错误现象**: 程序启动后立即退出，可能显示端口占用错误

**解决方法**:
1. 检查端口8080是否被占用：
```cmd
netstat -an | find "8080"
```

2. 如果被占用，修改`.env`文件中的端口：
```env
APP_PORT=8081
```

### 3. 配置文件问题
**错误现象**: 程序启动失败，提示配置错误

**解决方法**:
1. 确保`.env`文件存在且格式正确
2. 检查配置文件中的特殊字符和编码
3. 确保没有多余的空格或换行符

### 4. 网络连接问题
**错误现象**: 程序启动后连接数据库失败

**解决方法**:
1. 检查网络连接
2. 确认防火墙设置允许程序访问网络
3. 验证MySQL和Redis服务器地址是否正确

### 5. 权限问题
**错误现象**: 程序无法创建SQLite数据库文件

**解决方法**:
1. 以管理员身份运行程序
2. 确保程序目录有写入权限
3. 检查杀毒软件是否阻止了程序运行

## 详细调试步骤

### 步骤1：文件完整性检查
确保以下文件都在同一目录下：
```
question-bank-manager.exe (或 question-bank-manager-nocgo.exe)
.env
static/
├── index.html
├── pages/
└── assets/
```

### 步骤2：使用调试脚本
运行`debug.bat`脚本，它会：
- 检查系统环境
- 验证必需文件
- 检查端口占用
- 显示详细的启动日志

### 步骤3：检查系统兼容性
- 确保Windows版本支持（Windows 7及以上）
- 确认是64位系统（程序编译为64位）
- 检查是否安装了必要的运行时库

### 步骤4：网络连接测试
测试到远程服务器的连接：
```cmd
telnet 47.96.0.212 3380
telnet 47.96.0.212 6379
```

## 替代启动方法

### 方法1：使用PowerShell
```powershell
.\question-bank-manager.exe
```

### 方法2：创建快捷方式
1. 右键点击exe文件
2. 选择"创建快捷方式"
3. 右键点击快捷方式，选择"属性"
4. 在"目标"后添加参数（如果需要）

### 方法3：使用任务计划程序
1. 打开任务计划程序
2. 创建基本任务
3. 设置程序路径和工作目录

## 日志收集

如果问题仍然存在，请收集以下信息：

1. **系统信息**:
```cmd
systeminfo
```

2. **程序版本信息**:
```cmd
question-bank-manager.exe --version
```

3. **详细错误日志**:
```cmd
question-bank-manager.exe > error.log 2>&1
```

4. **网络连接状态**:
```cmd
netstat -an > network.log
```

## 联系支持

如果以上方法都无法解决问题，请提供：
1. Windows版本和架构
2. 错误日志内容
3. 使用的启动方法
4. 网络环境描述

## 快速解决方案总结

1. **首选方案**: 使用`question-bank-manager-nocgo.exe`
2. **调试方案**: 运行`debug.bat`查看详细错误
3. **兼容性方案**: 安装Visual C++ Redistributable
4. **网络方案**: 检查防火墙和网络连接
5. **权限方案**: 以管理员身份运行
