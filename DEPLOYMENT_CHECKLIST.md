# Windows部署检查清单

## 📋 部署前检查

### ✅ 文件完整性检查
- [ ] `question-bank-manager-nocgo.exe` (16MB) - 推荐版本
- [ ] `question-bank-manager.exe` (13MB) - 标准版本
- [ ] `.env` 配置文件
- [ ] `static/` 目录及所有子文件
- [ ] `start-nocgo.bat` 启动脚本
- [ ] `debug.bat` 调试脚本
- [ ] 文档文件 (README.md, TROUBLESHOOTING.md 等)

### ✅ 配置文件检查
打开 `.env` 文件，确认以下配置：
- [ ] `MYSQL_HOST=***********`
- [ ] `MYSQL_PORT=3380`
- [ ] `REDIS_HOST=***********`
- [ ] `REDIS_PORT=6379`
- [ ] `APP_PORT=8080` (或其他可用端口)
- [ ] `ADMIN_PHONE=***********`
- [ ] 阿里云服务配置完整

### ✅ 系统环境检查
- [ ] Windows 7/8/10/11 或 Windows Server 2012+
- [ ] 64位操作系统
- [ ] 至少512MB可用内存
- [ ] 至少100MB可用磁盘空间
- [ ] 网络连接正常

## 🚀 部署步骤

### 步骤1：文件部署
1. [ ] 创建程序目录 (建议: `C:\QuestionBankManager\`)
2. [ ] 复制所有文件到目标目录
3. [ ] 确保目录路径不包含中文字符
4. [ ] 检查文件权限 (确保可执行)

### 步骤2：网络测试
测试到远程服务器的连接：
```cmd
# 测试MySQL连接
telnet *********** 3380

# 测试Redis连接  
telnet *********** 6379
```
- [ ] MySQL连接正常
- [ ] Redis连接正常

### 步骤3：端口检查
```cmd
# 检查端口8080是否被占用
netstat -an | find "8080"
```
- [ ] 端口8080可用 (或已修改为其他端口)

### 步骤4：防火墙配置
- [ ] 允许程序访问网络
- [ ] 开放应用端口 (默认8080)
- [ ] 允许访问外部MySQL端口 (3380)
- [ ] 允许访问外部Redis端口 (6379)
- [ ] 允许HTTPS访问 (阿里云服务)

## 🧪 启动测试

### 测试1：调试模式启动
1. [ ] 双击 `debug.bat`
2. [ ] 观察启动日志
3. [ ] 确认无错误信息
4. [ ] 记录任何警告信息

### 测试2：正常模式启动
1. [ ] 双击 `start-nocgo.bat`
2. [ ] 等待启动完成
3. [ ] 确认显示 "Server starting on port :8080"
4. [ ] 程序保持运行状态

### 测试3：Web界面访问
1. [ ] 打开浏览器
2. [ ] 访问 `http://localhost:8080`
3. [ ] 确认显示登录页面
4. [ ] 界面样式正常显示

### 测试4：登录功能测试
1. [ ] 输入手机号: ***********
2. [ ] 点击"获取验证码"
3. [ ] 确认验证码发送成功
4. [ ] 输入验证码并登录
5. [ ] 成功进入管理界面

### 测试5：题库功能测试
1. [ ] 访问题库管理页面
2. [ ] 测试题目列表加载
3. [ ] 测试搜索功能
4. [ ] 测试筛选功能
5. [ ] 测试题目详情查看

## 🔧 故障排除

### 如果程序闪退
1. [ ] 使用 `debug.bat` 查看错误
2. [ ] 尝试 `question-bank-manager-nocgo.exe`
3. [ ] 检查是否缺少运行时库
4. [ ] 以管理员身份运行

### 如果网络连接失败
1. [ ] 检查防火墙设置
2. [ ] 测试网络连通性
3. [ ] 验证配置文件
4. [ ] 检查DNS解析

### 如果端口被占用
1. [ ] 修改 `.env` 中的 `APP_PORT`
2. [ ] 重启程序
3. [ ] 更新访问地址

## 📊 性能监控

### 启动后检查
- [ ] CPU使用率正常 (<10%)
- [ ] 内存使用量合理 (<100MB)
- [ ] 网络连接稳定
- [ ] 日志输出正常

### 功能验证
- [ ] 短信发送功能正常
- [ ] 图片上传功能正常
- [ ] 数据库操作正常
- [ ] 缓存同步正常

## 📝 部署完成确认

### 最终检查清单
- [ ] 程序正常启动
- [ ] Web界面可访问
- [ ] 登录功能正常
- [ ] 题库管理功能正常
- [ ] 文件上传功能正常
- [ ] 所有配置正确
- [ ] 性能表现良好
- [ ] 错误日志为空

### 交付文档
- [ ] 提供访问地址
- [ ] 提供管理员账号信息
- [ ] 提供操作手册
- [ ] 提供故障排除指南
- [ ] 提供技术支持联系方式

## 🎯 部署成功标志

当以下条件全部满足时，部署成功：

1. ✅ 程序稳定运行超过5分钟
2. ✅ Web界面完全正常显示
3. ✅ 登录流程完整可用
4. ✅ 题库管理功能正常
5. ✅ 无严重错误日志
6. ✅ 性能指标正常
7. ✅ 用户可以正常使用所有功能

---

**部署完成时间**: ___________  
**部署人员**: ___________  
**验收人员**: ___________  
**备注**: ___________
