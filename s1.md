使用go开发一个题库管理的应用，用到的业务配套如下；

业务配套Mysql8、redis、阿里云Oss，SQLite数据库、最终需要封装成exe可以在windows安装运行的软件。

我会提供html界面给你，然后你帮我将功能处理完善


sql示例；

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for questions
-- ----------------------------
DROP TABLE IF EXISTS `questions`;
CREATE TABLE `questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cache_key_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被哈希化的缓存键名字',
  `question_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '问题类型',
  `question_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '问题内容',
  `option_a` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项A',
  `option_b` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项B',
  `option_c` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项C',
  `option_d` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项D',
  `option_y` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项Y',
  `option_n` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题选项N',
  `answer` json DEFAULT NULL COMMENT '问题答案',
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '问题解析',
  `user_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '问题对应的图片名称',
  `image_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户提交的图片URL地址',
  `qwen_raw` json DEFAULT NULL COMMENT 'Qwen返回的原始数据',
  `deepseek_raw` json DEFAULT NULL COMMENT 'DeepSeek返回的原始数据',
  `qwen_parsed` json DEFAULT NULL COMMENT '被格式化解析后的Qwen数据',
  `is_verified` tinyint DEFAULT '0' COMMENT '是否已经验证过',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cache_key_hash` (`cache_key_hash`) COMMENT '缓存键哈希索引',
  KEY `idx_question_type` (`question_type`) COMMENT '问题类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
  KEY `idx_questions_cache_key_hash` (`cache_key_hash`),
  KEY `idx_questions_question_type` (`question_type`)
) ENGINE=InnoDB AUTO_INCREMENT=795 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目数据表';

SET FOREIGN_KEY_CHECKS = 1;



id
question_type
question_text
option_a
option_b
option_c
option_d
option_y
option_n
answer
analysis
user_image
image_url
qwen_raw
deepseek_raw
qwen_parsed
is_verified
created_at
updated_at


配置信息示例；
# redis
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0

# mysql
MySQL_8数据库
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4

# 阿里云oss
AccessKey ID
LTAI5tBdn5WuVw49f6VDUo1T

AccessKey Secret
******************************

bucket
lnterstellar

Endpoint（地域节点）
oss-cn-zhangjiakou.aliyuncs.com

Bucket 域名
lnterstellar.oss-cn-zhangjiakou.aliyuncs.com


目录upimggeuc   避免与其他项目冲突



# 阿里云短信
AccessKey ID
LTAI5tBdn5WuVw49f6VDUo1T

AccessKey Secret
******************************

签名
青岛果沐云计算


模版CODE
SMS_465946164


变量
${code}



业务逻辑描述；

使用python开发一款题库管理软件。主要作用对题库内的题目进行增删改查；

业务配套Mysql8、redis、阿里云Oss，SQLite数据库、最终需要封装成exe可以在windows安装运行的软件。

1. 使用权限配置
    内置一个固定的账号的管理员，账号为15688515913，需要使用短信验证码验证通过后进入软件。这里需要使用SQLite数据库。需要建立一个登录界面，固定手机号，点击获取短信验证码进行登录。登陆后进入题库管理界面。SQLite数据库的作用就是存储admin账号以及短信验证码的校验使用。

2. 题库管理(题库是一个mysql远程数据库。仅可以对数据库questions表中的数据进行增删改查，禁止进行结构更改或表增加删除。)

    2.1 题库问题展示界面
    允许搜索题库，仅搜索问题字段question_text，支持模糊的关键字搜索。
    问题带有两个状态is_verified字段为0表示未验证，为1表示已经验证，允许对问题进行is_verified的直接操作，点击已经验证，则将对应问题的is_verified变更为1。

    管理界面展示的字段排序
    id
    question_type
    question_text
    user_image
    image_url
    option_a
    option_b
    option_c
    option_d
    option_y
    option_n
    answer
    analysis
    is_verified
    

    允许对题目进行新增、编辑、删除。

3. 新增问题需要的字段
id  //自增  无需对用户展示
cache_key_hash  //必填项，新增保存时需要校验数据库内是否已经存在，至少存在1个相同值才可新增，如果不存在则提示键值对错误，并未查询到关联问题。
question_type   //必填项，下啦菜单，只能选择多选题、单选题、判断题 三种
question_text   //必填项，没有要求
option_a        //识别判断，如果选择多选题或判断题则展示ABCD四个选项可以填写，如果选择判断题，则只展示YN两个输入框可以填写。
option_b
option_c
option_d
option_y
option_n    // 提交保存时option判断规则为，若YN填写则ABCD不可以填写，若ABCD填写则YN不可以填写，ABCD任何一个填写了值，就必须ABCD四个都存在值，YN任意一个填写了 就必须两个都填写。否则不可以成功提交。
answer      //必填项，如果选择多选题，answer会出现四个输入框输入答案，提交保存时需要校验至少两个表单存在值，最后入库时应该是json的格式，示例{"A": "扰乱交通秩序", "B": "易引发交通事故", "C": "影响正常通行"} 系统自动解析成示例的json格式  ，对于判断题与单选题，一个答案也需要json入库。
analysis   //必填项，无要求
user_image  //非必填项，但需要对用户展示，用户可以上传图片，并将图片上传到阿里云Oss，然后将图片的url地址入库。
image_url   //非必填项 且新增问题时无需显示
qwen_raw    //非必填项，且新增问题时无需显示
deepseek_raw    //非必填项，且新增问题时无需显示
qwen_parsed     //非必填项，且新增问题时无需显示
is_verified     //必填项，默认为1，已验证，新增问题时才默认为1.编辑问题的时候是取原数据。需要将0 = 未验证   1 = 已验证   进行文字映射。下拉框选择。



4. 编辑问题时修改的字段
id  //禁止修改
cache_key_hash  //禁止修改
question_type   //允许修改，不能为空
question_text   //允许修改，不能为空
option_a        //识别判断，如果选择多选题或判断题则展示ABCD四个选项可以填写，如果选择判断题，则只展示YN两个输入框可以填写。
option_b
option_c
option_d
option_y
option_n    // 提交保存时option判断规则为，若YN填写则ABCD不可以填写，若ABCD填写则YN不可以填写，ABCD任何一个填写了值，就必须ABCD四个都存在值，YN任意一个填写了 就必须两个都填写。否则不可以成功提交。
answer      //必填项，如果选择多选题，answer会出现四个输入框输入答案，提交保存时需要校验至少两个表单存在值，最后入库时应该是json的格式，示例{"A": "扰乱交通秩序", "B": "易引发交通事故", "C": "影响正常通行"} 系统自动解析成示例的json格式  ，对于判断题与单选题，一个答案也需要json入库。
analysis   //允许修改，不能为空
user_image  //非必填项，但需要对用户展示，用户可以上传图片，并将图片上传到阿里云Oss，然后将图片的url地址入库。
image_url   //禁止修改，且编辑问题时无需显示
qwen_raw    //非必填项，且编辑问题时无需显示
deepseek_raw    //非必填项，且编辑问题时无需显示
qwen_parsed     //非必填项，且编辑问题时无需显示
is_verified     //必填项，默认为1，已验证



5. 新增与修改会触发redis清除动作，新增与修改任意题目后，需要使用题目的cache_key_hash对redis进行匹配删除对应的键值对。cache_key_hash是redis的键，这样做的目的是为了让mysql与redis中的数据一致。
不需要额外考虑其他业务，只做清除即可，在其他的api服务中会自动对数据进行校准。



6. 界面设计，整洁，色彩多样化，布局合理，特别是对于题目、解析等字段过长的需要控制好换行，控制好适配等问题。

7. 远程数据表结构参考sql.md文件

8. 相关配置信息参考config.md文件



html模版放置在了html目录下。自己提取使用；