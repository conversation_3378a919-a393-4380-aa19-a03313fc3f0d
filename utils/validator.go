package utils

import (
	"errors"
	"question-bank-manager/models"
	"regexp"
)

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	pattern := `^1[3-9]\d{9}$`
	matched, _ := regexp.MatchString(pattern, phone)
	return matched
}

// ValidateQuestionRequest 验证题目请求数据
func ValidateQuestionRequest(req *models.QuestionRequest) error {
	// 验证题目类型
	if req.QuestionType != "单选题" && req.QuestionType != "多选题" && req.QuestionType != "判断题" {
		return errors.New("题目类型只能是：单选题、多选题、判断题")
	}

	// 验证选项逻辑
	hasABCD := (req.OptionA != nil && *req.OptionA != "") ||
		(req.OptionB != nil && *req.OptionB != "") ||
		(req.OptionC != nil && *req.OptionC != "") ||
		(req.OptionD != nil && *req.OptionD != "")

	hasYN := (req.OptionY != nil && *req.OptionY != "") ||
		(req.OptionN != nil && *req.OptionN != "")

	// ABCD和YN不能同时存在
	if hasABCD && hasYN {
		return errors.New("ABCD选项和YN选项不能同时填写")
	}

	// 如果是判断题，必须使用YN选项
	if req.QuestionType == "判断题" {
		if !hasYN {
			return errors.New("判断题必须填写Y和N选项")
		}
		if (req.OptionY == nil || *req.OptionY == "") || (req.OptionN == nil || *req.OptionN == "") {
			return errors.New("判断题的Y和N选项都必须填写")
		}
	}

	// 如果是单选题或多选题，必须使用ABCD选项
	if req.QuestionType == "单选题" || req.QuestionType == "多选题" {
		if !hasABCD {
			return errors.New("单选题和多选题必须填写ABCD选项")
		}
		// 检查ABCD是否都有值
		if (req.OptionA == nil || *req.OptionA == "") ||
			(req.OptionB == nil || *req.OptionB == "") ||
			(req.OptionC == nil || *req.OptionC == "") ||
			(req.OptionD == nil || *req.OptionD == "") {
			return errors.New("ABCD选项都必须填写")
		}
	}

	// 验证答案
	if len(req.Answer) == 0 {
		return errors.New("答案不能为空")
	}

	// 多选题至少需要两个答案
	if req.QuestionType == "多选题" && len(req.Answer) < 2 {
		return errors.New("多选题至少需要两个答案")
	}

	// 单选题和判断题只能有一个答案
	if (req.QuestionType == "单选题" || req.QuestionType == "判断题") && len(req.Answer) != 1 {
		return errors.New("单选题和判断题只能有一个答案")
	}

	return nil
}
