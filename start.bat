@echo off
title 题库管理系统
color 0A

echo ========================================
echo           题库管理系统
echo ========================================
echo.

REM 检查可执行文件是否存在
if not exist "question-bank-manager.exe" (
    echo [错误] 找不到 question-bank-manager.exe 文件
    echo 请确保可执行文件在当前目录下
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist ".env" (
    echo [错误] 找不到 .env 配置文件
    echo 请确保配置文件在当前目录下
    pause
    exit /b 1
)

REM 检查静态文件目录是否存在
if not exist "static" (
    echo [错误] 找不到 static 静态文件目录
    echo 请确保静态文件目录在当前目录下
    pause
    exit /b 1
)

echo [信息] 正在启动题库管理系统...
echo [信息] 请稍候，系统正在初始化...
echo.

REM 启动应用程序
question-bank-manager.exe

REM 如果程序异常退出，显示错误信息
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [错误] 应用程序异常退出，错误代码: %ERRORLEVEL%
    echo 请检查配置文件和网络连接
    echo.
)

echo.
echo 应用程序已停止运行
pause
