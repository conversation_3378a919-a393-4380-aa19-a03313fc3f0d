@echo off
title 题库管理系统 - 调试模式
color 0A

echo ========================================
echo           题库管理系统
echo           调试启动模式
echo ========================================
echo.

REM 显示当前目录
echo [调试] 当前目录: %CD%
echo.

REM 检查可执行文件是否存在
if not exist "question-bank-manager.exe" (
    echo [错误] 找不到 question-bank-manager.exe 文件
    echo 请确保可执行文件在当前目录下
    echo.
    echo [调试] 当前目录文件列表:
    dir /b
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist ".env" (
    echo [错误] 找不到 .env 配置文件
    echo 请确保配置文件在当前目录下
    echo.
    echo [调试] 当前目录文件列表:
    dir /b
    pause
    exit /b 1
)

REM 检查静态文件目录是否存在
if not exist "static" (
    echo [错误] 找不到 static 静态文件目录
    echo 请确保静态文件目录在当前目录下
    echo.
    echo [调试] 当前目录文件列表:
    dir /b
    pause
    exit /b 1
)

echo [信息] 文件检查完成，所有必需文件都存在
echo [信息] 正在启动题库管理系统...
echo [信息] 如果程序闪退，请查看下方的错误信息
echo.

REM 启动应用程序并捕获输出
echo [调试] 执行命令: question-bank-manager.exe
echo ----------------------------------------
question-bank-manager.exe 2>&1

REM 显示退出代码
echo ----------------------------------------
echo [调试] 程序退出代码: %ERRORLEVEL%

REM 如果程序异常退出，显示错误信息
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [错误] 应用程序异常退出
    echo 可能的原因:
    echo 1. 缺少必要的运行时库
    echo 2. 配置文件格式错误
    echo 3. 网络连接问题
    echo 4. 端口被占用
    echo 5. 权限不足
    echo.
)

echo.
echo 按任意键退出...
pause > nul
